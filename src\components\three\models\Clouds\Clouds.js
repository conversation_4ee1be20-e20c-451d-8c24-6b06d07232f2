import * as THREE from "three";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants";
import { texturePreloader } from "../../utils/TexturePreloader.js";

class Clouds {
  constructor(scene, { isActive = true, renderer } = {}) {
    this.scene = scene;
    this.isActive = isActive;
    this.renderer = renderer;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.texture = null;

    // Animation properties
    this.animationSpeed = {
      x: 0.0002, // 增加X轴旋转速度
      y: 0.0005, // 增加Y轴旋转速度，模拟风向
      z: 0.0001, // 添加Z轴旋转，增加动态效果
    };
    this.time = 0; // 用于创建更复杂的动画

    // Animation control flags
    this.animationFlags = {
      rotation: true, // 控制旋转动画
      pulse: true, // 控制脉动效果
      opacity: true, // 控制透明度动画
    };

    this.init();
  }

  async init() {
    await this.loadTexture();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();
  }

  async loadTexture() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.clouds) {
        console.log("Clouds: 使用预加载的纹理");
        this.texture = preloadedTextures.clouds;
      } else {
        console.log("Clouds: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        this.texture = await this.loadTexturePromise(textureLoader, PATHS.clouds);
      }

      // Set anisotropy for better quality
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.texture.anisotropy = maxAnisotropy;

      console.log("Clouds: 纹理加载完成");
    } catch (error) {
      console.error("Error loading Clouds texture:", error);
    }
  }

  loadTexturePromise(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.material = new THREE.MeshPhongMaterial({
      map: this.texture,
      transparent: true,
      opacity: 0.2,
      color: "#ffffff",
      // 增强云层的视觉效果
      alphaTest: 0.1, // 提高透明度测试阈值，让云层边缘更清晰
      side: THREE.DoubleSide, // 双面渲染，确保从各个角度都能看到云层
      depthWrite: false, // 禁用深度写入，避免透明度问题
      blending: THREE.NormalBlending, // 使用正常混合模式
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.position.set(0, 0, 0);
    this.mesh.scale.set(1.001, 1.001, 1.001);
    this.mesh.receiveShadow = true;
  }

  addToScene() {
    if (this.scene && this.mesh) {
      // this.scene.add(this.mesh);
    }
  }

  // 计算旋转动画
  calculateRotationAnimation() {
    if (!this.mesh || !this.animationFlags.rotation) return;

    this.mesh.rotation.x += this.animationSpeed.x;
    this.mesh.rotation.y += this.animationSpeed.y;
    this.mesh.rotation.z += this.animationSpeed.z;
  }

  // 计算脉动效果
  calculatePulseAnimation() {
    if (!this.mesh || !this.animationFlags.pulse) return;

    const pulseFactor = 1 + Math.sin(this.time * 0.5) * 0.002;
    this.mesh.scale.set(1.001 * pulseFactor, 1.001 * pulseFactor, 1.001 * pulseFactor);
  }

  // 计算透明度动画
  calculateOpacityAnimation() {
    if (!this.material || !this.animationFlags.opacity) return;

    const opacityVariation = 0.5 + Math.sin(this.time * 0.3) * 0.1;
    this.material.opacity = Math.max(0.3, Math.min(0.7, opacityVariation));
  }

  // 更新时间计数器
  updateTimeCounter() {
    this.time += 0.01;
  }

  update() {
    if (this.mesh) {
      // 更新时间计数器
      this.updateTimeCounter();

      // 执行各种动画计算
      // this.calculateRotationAnimation(); // 基础旋转动画 - 模拟大气环流
      // this.calculatePulseAnimation(); // 添加微妙的脉动效果，模拟云层厚度变化
      // this.calculateOpacityAnimation(); // 动态调整透明度，模拟云层密度变化
    }
  }

  // 新增方法：设置动画速度
  setAnimationSpeed(speedMultiplier = 1) {
    this.animationSpeed = {
      x: 0.0002 * speedMultiplier,
      y: 0.0005 * speedMultiplier,
      z: 0.0001 * speedMultiplier,
    };
  }

  // 新增方法：暂停/恢复动画
  pauseAnimation() {
    this.animationSpeed = { x: 0, y: 0, z: 0 };
  }

  // 新增方法：重置动画到默认速度
  resumeAnimation() {
    this.animationSpeed = {
      x: 0.0002,
      y: 0.0005,
      z: 0.0001,
    };
  }

  // 新增方法：控制特定动画效果的开关
  toggleRotationAnimation(enabled = true) {
    this.animationFlags.rotation = enabled;
  }

  togglePulseAnimation(enabled = true) {
    this.animationFlags.pulse = enabled;
  }

  toggleOpacityAnimation(enabled = true) {
    this.animationFlags.opacity = enabled;
  }

  // 新增方法：获取当前动画状态
  getAnimationStatus() {
    return {
      rotation: this.animationFlags.rotation,
      pulse: this.animationFlags.pulse,
      opacity: this.animationFlags.opacity,
      speed: this.animationSpeed,
      time: this.time,
    };
  }

  destroy() {
    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of texture
    if (this.texture) {
      this.texture.dispose();
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.texture = null;
  }
}

// Export both named and default exports for flexibility
export { Clouds };
export default Clouds;
